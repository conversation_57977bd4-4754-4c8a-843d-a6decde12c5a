'use client'
import { use<PERSON><PERSON>back, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import {
  TCatchMessage,
  useDebounceFn,
  useLoadingContext,
  useRateLimitHandler,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { ROUTE, TRACK_EVENT } from '@ninebot/core/src/constants'
import {
  useAddCartItemsToCheckoutMutation,
  useDeleteCartProductsMutation,
  useSelectCartProductsMutation,
} from '@ninebot/core/src/services'
import {
  addCartDeleteProductUIDs,
  cartAllAvailableProductsUidSelector,
  cartDeleteProductUIDsSelector,
  cartIsAllSelectedDeleteProducts,
  cartIsAllSelectedProductsSelector,
  cartIsEditModeSelector,
  cartSelectedProductsUidSelector,
  deleteCartDeleteProductUIDs,
  deleteCartProducts,
  deselectedCartProducts,
  selectCartAllProductIds,
  selectedCartProducts,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { GqlError, PRECISE_RATE_LIMIT, resolveCatchMessage, sleep } from '@ninebot/core/src/utils'
import { Button, Dialog } from 'antd-mobile'

import { CustomCheckbox } from '@/components'

import CurrentPrice from './CurrentPrice'

const BottomActionBar = () => {
  const getI18nString = useTranslations('Common')
  const dispatch = useAppDispatch()
  const toast = useToastContext()
  const loading = useLoadingContext()
  const { openPage } = useNavigate()
  const { reportEvent } = useVolcAnalytics()

  const isAllSelectedCartProducts = useAppSelector(cartIsAllSelectedProductsSelector)
  const allAvailableProductsUid = useAppSelector(cartAllAvailableProductsUidSelector)

  const selectedProductsUid = useAppSelector(cartSelectedProductsUidSelector)
  const isEditMode = useAppSelector(cartIsEditModeSelector)
  const cartAllProductIds = useAppSelector((state) => selectCartAllProductIds(state))
  const isAllSelectedDeleteProducts = useAppSelector(cartIsAllSelectedDeleteProducts)
  const deleteProductUIDs = useAppSelector(cartDeleteProductUIDsSelector)

  const [selectCartProductsMutation] = useSelectCartProductsMutation()
  const [addCartItemsToCheckoutMutation, { isLoading: addCartItemsToCheckoutLoading }] =
    useAddCartItemsToCheckoutMutation()
  const [deleteCartProductsMutation] = useDeleteCartProductsMutation()

  // 限流处理 - 结算按钮
  const checkoutRateLimit = useRateLimitHandler({
    mode: 'button',
    originalText: getI18nString('go_checkout'),
  })

  /**
   * 是否全选
   */
  const isAllChecked = useMemo(() => {
    if (isEditMode) {
      return isAllSelectedDeleteProducts
    }

    return isAllSelectedCartProducts
  }, [isEditMode, isAllSelectedCartProducts, isAllSelectedDeleteProducts])

  /**
   * 结算按钮是否 disabled
   */
  const isDisabledCheckoutBtn = useMemo(() => {
    return addCartItemsToCheckoutLoading || checkoutRateLimit.isDisabled
  }, [addCartItemsToCheckoutLoading, checkoutRateLimit.isDisabled])

  /**
   * 产品全选/取消全选请求事件
   */
  const handleAllCheckProductsChange = useCallback(
    async (status: boolean) => {
      try {
        loading.show()
        const response = await selectCartProductsMutation({
          isSelectAll: !status,
          items: [],
        }).unwrap()

        await sleep(500)
        if (response?.selectProductsInShippingCart) {
          if (status) {
            dispatch(deselectedCartProducts(selectedProductsUid))
          } else {
            dispatch(selectedCartProducts(allAvailableProductsUid))
          }
        }
        loading.hide()
      } catch (error) {
        await sleep(500)
        loading.hide()
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: String(resolveCatchMessage(error as TCatchMessage)),
        })
      }
    },
    [
      dispatch,
      selectCartProductsMutation,
      selectedProductsUid,
      allAvailableProductsUid,
      toast,
      loading,
    ],
  )

  /**
   * 产品全选/取消全选事件
   */
  const handleAllCheckChange = useCallback(() => {
    if (isEditMode) {
      if (isAllChecked) {
        dispatch(deleteCartDeleteProductUIDs(cartAllProductIds))
      } else {
        dispatch(addCartDeleteProductUIDs(cartAllProductIds))
      }
      return
    }

    return handleAllCheckProductsChange(isAllChecked)
  }, [isEditMode, handleAllCheckProductsChange, isAllChecked, cartAllProductIds, dispatch])

  /**
   * 点击结算按钮事件
   */
  const { run: handleCheckout } = useDebounceFn(async () => {
    if (!selectedProductsUid.length) {
      toast.show({
        icon: 'info',
        content: getI18nString('please_select_product'),
      })
      return
    }

    reportEvent(TRACK_EVENT.shop_cart_checkout_click, {
      button_id: 'shop_checkout',
    })

    try {
      loading.show()
      const response = await addCartItemsToCheckoutMutation({
        itemIds: selectedProductsUid,
      }).unwrap()

      const result = response?.addCartItemToCheckout

      if (result?.status && result?.cart?.id) {
        await sleep(500)
        loading.hide()
        await sleep(500)
        openPage({
          route: ROUTE.checkout,
        })
      }

      if (result?.add_to_checkout_user_errors?.length) {
        await sleep(500)
        loading.hide()
        toast.show({
          icon: 'fail',
          content: result?.add_to_checkout_user_errors?.[0]?.message as string,
        })
      }
    } catch (error) {
      loading.hide()

      const err = error as GqlError
      // 精准限流
      if (err?.type === PRECISE_RATE_LIMIT) {
        checkoutRateLimit.handleError(err.retryMs!)
      }

      // 如果不是限流错误，显示普通错误提示
      toast.show({
        icon: 'fail',
        content: String(resolveCatchMessage(error as TCatchMessage)),
      })
    }
  })

  /**
   * 产品删除请求事件
   */
  const handleProductsDelete = useCallback(
    async (productIds: string[]) => {
      try {
        await sleep(500)
        loading.show()
        const response = await deleteCartProductsMutation({
          itemIds: productIds,
        }).unwrap()

        if (response?.deleteProductsFromShippingCart) {
          await sleep(500)
          loading.hide()
          await sleep(500)
          dispatch(deleteCartProducts(productIds))

          toast.show({
            icon: 'success',
            content: getI18nString('delete_success'),
          })
        }
      } catch (error) {
        await sleep(500)
        loading.hide()
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: String(resolveCatchMessage(error as TCatchMessage)),
        })
      }
    },
    [deleteCartProductsMutation, dispatch, getI18nString, toast, loading],
  )

  /**
   * 批量删除商品
   */
  const { run: handleDialogProductsDelete } = useDebounceFn(async () => {
    if (!deleteProductUIDs.length) {
      toast.show({
        icon: 'info',
        content: getI18nString('please_select_product'),
      })
      return
    }

    Dialog.confirm({
      bodyClassName: 'custom-dialog-confirm',
      content: getI18nString('confirm_delete_products', { key: deleteProductUIDs.length }),
      cancelText: getI18nString('thinking'),
      confirmText: getI18nString('delete'),
      onConfirm: () => {
        handleProductsDelete(deleteProductUIDs)
      },
    })
  })

  return (
    <div className="max-container fixed bottom-0 z-10 flex h-[68px] w-full max-w-mobile-base bg-white">
      {/* 促销提示信息 */}
      {/* {promotionTip && (
        <div className="absolute -top-8 left-0 right-0 bg-red-50 px-base-12 py-base text-sm text-secondary-red-200">
          {promotionTip}
        </div>
      )} */}

      <div className="flex w-full items-center gap-base-12 px-base-12">
        {/* 购物车模式:显示全选框和商品数量 */}
        <CustomCheckbox checked={isAllChecked} onChange={handleAllCheckChange}>
          {getI18nString('checkbox_all')}
        </CustomCheckbox>

        {/* 右侧展示区和操作按钮 */}
        <div
          className={`flex flex-1 items-center ${isEditMode ? 'justify-end' : 'justify-between'}`}>
          {isEditMode ? (
            <Button
              className="nb-button h-[44px] w-[100px] !leading-none"
              color="primary"
              onClick={handleDialogProductsDelete}>
              {getI18nString('delete')}
            </Button>
          ) : (
            <>
              <CurrentPrice />
              <Button
                className={`nb-button ${checkoutRateLimit.isDisabled ? 'nb-button-no-padding' : ''}`}
                color="primary"
                disabled={isDisabledCheckoutBtn}
                onClick={handleCheckout}>
                {checkoutRateLimit.buttonText || getI18nString('go_checkout')}
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default BottomActionBar
