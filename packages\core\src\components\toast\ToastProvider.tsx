'use client'

import React, { createContext, useCallback, useContext, useMemo, useRef, useState } from 'react'

import { TBaseComponentProps } from '../../typings'

import Toast, { TToastProps } from './Toast'

const ToastContext = createContext<{
  show: (params: TToastProps) => void
  hide: () => void
}>({
  show: () => {},
  hide: () => {},
})

type TToastProvider = TBaseComponentProps & Partial<TToastProps>

/**
 * Toast provider
 */
const ToastProvider = (props: TToastProvider) => {
  const { children, ...providerProps } = props

  const [visible, setVisible] = useState(false)
  const [toastProps, setToastProps] = useState<Omit<TToastProps, 'visible'>>({ content: '' })
  const timeId = useRef<NodeJS.Timeout>()

  const hideToast = useCallback(() => {
    setVisible(false)
    setToastProps({ content: '' })
  }, [])

  const showToast = useCallback((params: Omit<TToastProps, 'visible'>) => {
    clearTimeout(timeId.current)

    timeId.current = setTimeout(() => {
      setVisible(true)
      setToastProps(params)
    }, 200)
  }, [])

  const contextValue = useMemo(() => {
    return {
      show: showToast,
      hide: hideToast,
    }
  }, [showToast, hideToast])

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <Toast visible={visible} onClose={hideToast} {...providerProps} {...toastProps} />
    </ToastContext.Provider>
  )
}

export const useToastContext = () => useContext(ToastContext)

ToastProvider.displayName = 'ToastProvider'

export default ToastProvider
