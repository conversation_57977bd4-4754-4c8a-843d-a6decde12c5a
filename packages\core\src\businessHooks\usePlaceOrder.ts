import { useCallback } from 'react'
import isUndefined from 'lodash-es/isUndefined'

import { useLoadingContext, useToastContext } from '../components'
import {
  SetBillingAddressOnCartInput,
  SetPaymentMethodAndPlaceOrderInput,
  SetShippingAddressesOnCartInput,
  SetShippingMethodsOnCartInput,
} from '../graphql/generated/graphql'
import {
  useSetBillingAddressOnCartMutation,
  useSetPaymentMethodAndPlaceOrderMutation,
  useSetShippingAddressesOnCartMutation,
  useSetShippingMethodsOnCartMutation,
} from '../services'
import { resolveCatchMessage, sleep, TCatchMessage } from '../utils'

/**
 * usePlaceOrder Hook
 *
 * 用于处理下单流程中的各种操作，包括设置收货地址、账单地址、配送方式以及支付方式并提交订单。
 *
 * @returns {Object} 包含下单操作相关的状态和方法。
 * @property {Function} handleSetShippingAddressesOnCart - 设置购物车的收货地址。
 * @property {boolean} setShippingAddressesOnCartLoading - 是否正在设置收货地址。
 * @property {Function} handleSetBillingAddressOnCart - 设置购物车的账单地址。
 * @property {boolean} setBillingAddressOnCartLoading - 是否正在设置账单地址。
 * @property {Function} handleSetShippingMethodsOnCart - 设置购物车的配送方式。
 * @property {boolean} setShippingMethodsOnCartLoading - 是否正在设置配送方式。
 * @property {Function} handleSetPaymentMethodAndPlaceOrder - 设置支付方式并提交订单。
 * @property {boolean} setPaymentMethodAndPlaceOrderLoading - 是否正在设置支付方式并提交订单。
 */
const usePlaceOrder = () => {
  const loading = useLoadingContext()
  const toast = useToastContext()

  const [setShippingAddressesOnCartMutation, { isLoading: setShippingAddressesOnCartLoading }] =
    useSetShippingAddressesOnCartMutation()
  const [setBillingAddressOnCartMutation, { isLoading: setBillingAddressOnCartLoading }] =
    useSetBillingAddressOnCartMutation()
  const [setShippingMethodsOnCartMutation, { isLoading: setShippingMethodsOnCartLoading }] =
    useSetShippingMethodsOnCartMutation()
  const [
    setPaymentMethodAndPlaceOrderMutation,
    { isLoading: setPaymentMethodAndPlaceOrderLoading },
  ] = useSetPaymentMethodAndPlaceOrderMutation()

  /**
   * 设置 shipping addresses
   */
  const handleSetShippingAddressesOnCart = useCallback(
    async (shippingAddressesOnCartInput: SetShippingAddressesOnCartInput) => {
      try {
        const response = await setShippingAddressesOnCartMutation({
          input: shippingAddressesOnCartInput,
        }).unwrap()

        return response?.setShippingAddressesOnCart
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })

        return null
      }
    },
    [toast, setShippingAddressesOnCartMutation],
  )

  /**
   * 设置 billing address
   */
  const handleSetBillingAddressOnCart = useCallback(
    async (billingAddressOnCartInput: SetBillingAddressOnCartInput) => {
      try {
        const response = await setBillingAddressOnCartMutation({
          input: billingAddressOnCartInput,
        }).unwrap()

        return response?.setBillingAddressOnCart
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })

        return null
      }
    },
    [toast, setBillingAddressOnCartMutation],
  )

  /**
   * 设置 shipping methods
   */
  const handleSetShippingMethodsOnCart = useCallback(
    async (shippingMethodInput: SetShippingMethodsOnCartInput) => {
      try {
        const response = await setShippingMethodsOnCartMutation({
          input: shippingMethodInput,
        }).unwrap()

        return response?.setShippingMethodsOnCart
      } catch (error) {
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })

        return null
      }
    },
    [toast, setShippingMethodsOnCartMutation],
  )

  /**
   * 设置 payment method，并且下单
   */
  const handleSetPaymentMethodAndPlaceOrder = useCallback(
    async (setPaymentMethodAndPlaceOrderInput: SetPaymentMethodAndPlaceOrderInput) => {
      try {
        loading.show()
        const response = await setPaymentMethodAndPlaceOrderMutation({
          input: setPaymentMethodAndPlaceOrderInput,
        }).unwrap()

        const result = response?.setPaymentMethodAndPlaceOrder

        if (!isUndefined(result?.order?.order_number)) {
          await sleep(500)
          loading.hide()
          return result.order
        }

        return null
      } catch (error) {
        await sleep(500)
        loading.hide()
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error as TCatchMessage) as string,
        })

        return null
      }
    },
    [toast, setPaymentMethodAndPlaceOrderMutation, loading],
  )

  return {
    handleSetShippingAddressesOnCart,
    setShippingAddressesOnCartLoading,
    handleSetBillingAddressOnCart,
    setBillingAddressOnCartLoading,
    handleSetShippingMethodsOnCart,
    setShippingMethodsOnCartLoading,
    handleSetPaymentMethodAndPlaceOrder,
    setPaymentMethodAndPlaceOrderLoading,
  }
}

export default usePlaceOrder
