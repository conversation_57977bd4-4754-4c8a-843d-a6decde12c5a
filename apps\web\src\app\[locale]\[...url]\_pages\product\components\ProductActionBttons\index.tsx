'use client'

import { Button } from 'antd'

import { useMediaQuery } from '@/hooks'

import { useProduct } from '../../context/ProductContext'
import QuantitySelector from '../ProductInfo/QuantitySelector'

interface productStatusType {
  isEverythingOutOfStock: boolean
  isOutOfStock: boolean
}

const ProductActionButtons = () => {
  const {
    onAddToCartBefore,
    isBuyNow,
    addCartLoading,
    buyNowLoading,
    productStatus,
    addToCartRateLimit,
    buyNowRateLimit,
  } = useProduct() as {
    onAddToCartBefore: (quantity: number) => void
    isBuyNow: boolean
    addCartLoading: boolean
    buyNowLoading: boolean
    productStatus: productStatusType
    addToCartRateLimit: {
      isDisabled: boolean
      buttonText: string
    }
    buyNowRateLimit: {
      isDisabled: boolean
      buttonText: string
    }
  }

  const getStatusStock = () => {
    return !(productStatus.isEverythingOutOfStock || productStatus.isOutOfStock)
  }

  const responsive = useMediaQuery()

  return (
    <div className="flex w-full items-center gap-base-12">
      {/* 数量选择 */}
      <div>
        <QuantitySelector />
      </div>

      <div
        className={`grid flex-1 items-center gap-base ${getStatusStock() && !isBuyNow ? 'grid-cols-2' : 'grid-cols-1'}`}>
        {getStatusStock() ? (
          isBuyNow ? (
            <Button
              onClick={() => onAddToCartBefore(2)}
              disabled={addCartLoading || buyNowLoading || buyNowRateLimit?.isDisabled}
              size={responsive?.['xll'] ? 'large' : 'middle'}
              type="primary"
              className={buyNowRateLimit?.isDisabled ? 'nb-button-no-padding' : ''}>
              {buyNowRateLimit?.buttonText || '立即购买'}
            </Button>
          ) : (
            <>
              <Button
                onClick={() => onAddToCartBefore(1)}
                disabled={addCartLoading || buyNowLoading || addToCartRateLimit?.isDisabled}
                size={responsive?.['xll'] ? 'large' : 'middle'}
                className={addToCartRateLimit?.isDisabled ? 'nb-button-no-padding' : ''}>
                {addToCartRateLimit?.buttonText || '加入购物车'}
              </Button>
              <Button
                onClick={() => onAddToCartBefore(2)}
                disabled={addCartLoading || buyNowLoading || buyNowRateLimit?.isDisabled}
                type="primary"
                size={responsive?.['xll'] ? 'large' : 'middle'}
                className={buyNowRateLimit?.isDisabled ? 'nb-button-no-padding' : ''}>
                {buyNowRateLimit?.buttonText || '立即购买'}
              </Button>
            </>
          )
        ) : (
          <Button disabled size={responsive?.['xll'] ? 'large' : 'middle'}>
            售罄
          </Button>
        )}
      </div>
    </div>
  )
}

export default ProductActionButtons
